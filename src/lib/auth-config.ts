// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { tokenRefreshManager } from '@/lib/token-refresh';
import { sessionManager } from '@/lib/session-manager';
import { RemoteOAuthService } from '@/services/RemoteOAuthService';

export const createAuthOptions = (): NextAuthOptions => {
  const secret = process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('NEXTAUTH_SECRET is required for authentication');
  }

  if (secret.length < 32) {
    throw new Error('NEXTAUTH_SECRET must be at least 32 characters long');
  }

  const authOptions: NextAuthOptions = {
    adapter: PrismaAdapter(prisma),
    providers: [
       {
         id: "member-portal",
         name: "Member Portal",
         type: "oauth",
         // Use explicit OAuth endpoints instead of OpenID Connect discovery
         authorization: {
           url: `${process.env.MEMBER_PORTAL_URL}/api/oauth/authorize`,
           params: {
             scope: "read:profile",
             response_type: "code",
           }
         },
         token: {
           url: `${process.env.MEMBER_PORTAL_URL}/api/oauth/token`,
         },
         userinfo: `${process.env.MEMBER_PORTAL_URL}/api/oauth/userinfo`,
         clientId: process.env.CLIENT_ID,
         clientSecret: process.env.CLIENT_SECRET,
         checks: ["pkce", "state"],
         allowDangerousEmailAccountLinking: false,
         profile(profile) {
           logger.debug('OAuth profile received', {
             profileKeys: Object.keys(profile),
             sub: profile.sub,
             email: profile.email
           });

           return {
             id: profile.sub,
             name: profile.name,
             email: profile.email,
             role: profile.role,
           };
         },
       },
     ],
    callbacks: {
       async signIn({ user, account, profile: _profile, email: _email, credentials: _credentials }) {
         // Log successful sign in
         if (user?.email) {
           logger.auth.signIn(user.email, account?.provider || 'unknown', {
             userId: user.id,
           });
         }
         return true;
       },
       async jwt({ token, user, account, profile }) {
         const context = {
           hasUser: !!user,
           hasAccount: !!account,
           hasProfile: !!profile,
           tokenKeys: Object.keys(token),
           email: user?.email || token?.email,
         };

         logger.debug('JWT callback triggered', context);

         if (account && user) {
           token.accessToken = account.access_token;
           token.id = user.id;
           token.role = user.role;

           // Create session tracking
           const expiresAt = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60); // 30 days
           sessionManager.createSession(
             user.id,
             user.email || '',
             user.role || 'USER',
             'unknown', // IP will be tracked in middleware
             'unknown', // User agent will be tracked in middleware
             expiresAt
           );

           logger.debug('JWT token updated with user data', {
             ...context,
             userId: user.id,
             email: user.email,
             role: user.role,
           });
         }

         // Handle OAuth callback errors
         if (account?.error) {
           logger.auth.oauthError(account.error, {
             ...context,
             accountError: account.error,
           });
           console.error('OAuth callback error:', account.error);
           throw new Error(`OAuth authentication failed: ${account.error}`);
         }

         // Log successful OAuth callback
         if (account && user) {
           console.log('OAuth callback successful:', {
             provider: account.provider,
             userId: user.id,
             userEmail: user.email,
           });
         }

         // Check if token needs refresh (for existing sessions)
         if (token.exp && token.id && typeof token.id === 'string') {
           const needsRefresh = tokenRefreshManager.shouldRefreshToken(token.exp, token.id);

           if (needsRefresh) {
             const currentToken = {
               accessToken: token.accessToken as string,
               expiresAt: token.exp,
             };

             const refreshedToken = await tokenRefreshManager.refreshToken(token.id, currentToken);

             if (refreshedToken) {
               token.accessToken = refreshedToken.accessToken;
               token.exp = refreshedToken.expiresAt;

               logger.info('Token refreshed successfully', {
                 userId: token.id,
                 email: token.email,
                 newExpiry: refreshedToken.expiresAt,
               });
             }
           }
         }

         return token;
       },
      async session({ session, token }) {
        const context = {
          hasSession: !!session,
          hasToken: !!token,
          tokenId: token?.id,
          tokenSub: token?.sub,
          tokenEmail: token?.email,
          tokenRole: token?.role,
          tokenKeys: token ? Object.keys(token) : [],
          email: session?.user?.email,
        };

        logger.debug('Session callback triggered', context);

        if (session.user && token) {
          // Use sub as the ID if id is not available
          const userId = (token.id || token.sub) as string;
          session.user.id = userId;
          session.user.role = (token.role || 'USER') as string;

          // Update session activity
          sessionManager.updateActivity(`session_${userId}_${Date.now()}`);

          logger.debug('Session updated with token data', {
            ...context,
            userId: session.user.id,
            email: session.user.email,
            role: session.user.role,
          });
        }
        return session;
      },
      async redirect({ url, baseUrl }) {
        // After successful OAuth, redirect to dashboard
        if (url.includes('/api/auth/callback')) {
          return `${baseUrl}/dashboard`;
        }
        
        // Always use absolute URLs for OAuth redirects
        if (url.startsWith('/')) {
          return `${baseUrl}${url}`;
        }

        // For absolute URLs, check if they're from our domain
        try {
          const parsedUrl = new URL(url);
          const parsedBaseUrl = new URL(baseUrl);

          if (parsedUrl.origin === parsedBaseUrl.origin) {
            return url;
          }
        } catch {
          // Invalid URL, fallback to base URL
          return baseUrl;
        }

        // External URLs are not allowed for security
        return baseUrl;
      },
    },
    pages: {
      error: '/auth/error',
    },
    session: {
      strategy: "jwt" as const,
      maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    cookies: {
      sessionToken: {
        name: `next-auth.session-token`,
        options: {
          httpOnly: true,
          sameSite: 'none', // Use 'none' for cross-origin OAuth flows
          path: '/',
          secure: true, // Must be true for sameSite: 'none'
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined
        },
      },
      callbackUrl: {
        name: `next-auth.callback-url`,
        options: {
          httpOnly: true,
          sameSite: 'none', // Use 'none' for cross-origin OAuth flows
          path: '/',
          secure: true, // Must be true for sameSite: 'none'
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined
        },
      },
      csrfToken: {
        name: `next-auth.csrf-token`,
        options: {
          httpOnly: true,
          sameSite: 'none', // Use 'none' for cross-origin OAuth flows
          path: '/',
          secure: true, // Must be true for sameSite: 'none'
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined
        },
      },
      pkceCodeVerifier: {
        name: `next-auth.pkce.code_verifier`,
        options: {
          httpOnly: true,
          sameSite: 'none', // Use 'none' for cross-origin OAuth flows
          path: '/',
          secure: true, // Must be true for sameSite: 'none'
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined,
          maxAge: 60 * 15, // 15 minutes for PKCE
        },
      },
      state: {
        name: `next-auth.state`,
        options: {
          httpOnly: true,
          sameSite: 'none', // Use 'none' for cross-origin OAuth flows
          path: '/',
          secure: true, // Must be true for sameSite: 'none'
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined,
          maxAge: 60 * 15, // 15 minutes for state parameter
        },
      },
    },
    debug: process.env.NODE_ENV === 'development', // Enable debug in development
    secret: secret,
    events: {
      async signIn(message) {
        if (message.user?.email) {
          logger.auth.signIn(message.user.email, 'member-portal', {
            userId: message.user.id,
            account: message.account?.provider,
          });
        }
      },
      async signOut(message) {
        if (message.token?.email) {
          const userId = message.token.sub || message.token.id;
          const accessToken = message.token.accessToken as string;

          // Log the signout initiation
          logger.auth.signOut(message.token.email, {
            userId: userId,
          });

          // 1. First, attempt remote logout to invalidate OAuth session on member portal
          if (RemoteOAuthService.isConfigured()) {
            try {
              const remoteLogoutResult = await RemoteOAuthService.logout({
                accessToken: accessToken,
                userId: userId,
                email: message.token.email
              });

              if (remoteLogoutResult.success) {
                logger.info('Remote OAuth logout successful', {
                  userId: userId,
                  email: message.token.email,
                  provider: 'member-portal'
                });
              } else {
                logger.warn('Remote OAuth logout failed, continuing with local cleanup', {
                  userId: userId,
                  email: message.token.email,
                  error: remoteLogoutResult.error
                });
              }
            } catch (error) {
              logger.error('Remote logout error, continuing with local cleanup', {
                userId: userId,
                email: message.token.email
              }, error as Error);
            }
          } else {
            logger.warn('Remote OAuth service not configured, skipping remote logout');
          }

          // 2. Clean up local custom session manager
          if (userId) {
            sessionManager.destroyUserSessions(userId);
          }

          // 3. Clean up token refresh manager
          if (userId) {
            tokenRefreshManager.cleanup(userId);
          }

          logger.info('Local logout cleanup completed', {
            userId: userId,
            email: message.token.email,
            provider: 'member-portal'
          });
        }
      },
      async error(message) {
        logger.error('NextAuth error occurred', {
          error: message.error,
          provider: message.provider,
          type: message.type
        });

        // Log specific OAuth callback errors
        if (message.error?.name === 'OAuthCallbackError') {
          logger.error('OAuth callback error details', {
            message: message.error.message,
            cause: message.error.cause,
            provider: message.provider
          });
        }
      },
    },
  };
  // Only log in debug mode
  if (process.env.NODE_ENV === 'development') {
    // console.log("Auth options created:", authOptions.providers);
  }
  return authOptions;
};