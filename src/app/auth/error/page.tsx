'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const callbackUrl = searchParams.get('callbackUrl') || '/';

  // Map error codes to user-friendly messages
  const errorMessages: Record<string, { title: string; message: string }> = {
    'OAuthAccountNotLinked': {
      title: 'Account Already Linked',
      message: 'This account is already linked with another profile. Please sign in with the original account or contact support.'
    },
    'OAuthCallback': {
      title: 'Authentication Failed',
      message: 'We couldn\'t complete the authentication process. This might be due to a session timeout or browser cookie settings. Please try again.'
    },
    'OAuthCallbackError': {
      title: 'OAuth Callback Error',
      message: 'There was an issue with the OAuth callback. This could be due to expired session cookies or browser security settings. Please clear your browser cookies and try again.'
    },
    'AccessDenied': {
      title: 'Access Denied',
      message: 'You do not have permission to access this resource.'
    },
    'Verification': {
      title: 'Verification Error',
      message: 'The verification token has expired or is invalid. Please try signing in again.'
    },
    'Callback': {
      title: 'OAuth Callback Failed',
      message: 'There was an issue with the authentication callback from the Member Portal. This could be due to a network issue or configuration problem.'
    },
    'TokenExchangeFailed': {
      title: 'Token Exchange Failed',
      message: 'Failed to exchange authorization code for access token. This could be due to a network issue or configuration problem.'
    },
    'UserInfoFailed': {
      title: 'User Info Retrieval Failed',
      message: 'Failed to retrieve user information from Member Portal. This could be due to a network issue or configuration problem.'
    },
    'UserCreationFailed': {
      title: 'User Creation Failed',
      message: 'Failed to create or update user in local database. Please contact support.'
    },
    'NetworkError': {
      title: 'Network Error',
      message: 'Network connectivity issue occurred during authentication. Please check your connection and try again.'
    },
    'OAuthInitiationFailed': {
      title: 'OAuth Initiation Failed',
      message: 'Failed to initiate OAuth flow. Please check configuration and try again.'
    },
    'ConfigurationError': {
      title: 'Configuration Error',
      message: 'Authentication service is not properly configured. Please contact support.'
    }
  };

  const errorInfo = errorMessages[error || ''] || {
    title: 'Authentication Error',
    message: 'An unexpected error occurred during authentication. Please try again.'
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 rounded-full bg-red-500/10 flex items-center justify-center mb-6">
            <svg className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          
          <h2 className="mt-6 text-3xl font-extrabold text-white">
            {errorInfo.title}
          </h2>
          
          <p className="mt-2 text-sm text-gray-300">
            {errorInfo.message}
          </p>
        </div>

        <div className="mt-8 space-y-4">
          <Link 
            href={`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-cyan-600 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
          >
            Try Again
          </Link>
          
          <Link 
            href="/"
            className="w-full flex justify-center py-3 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
          >
            Go to Homepage
          </Link>
        </div>
      </div>
    </div>
  );
}